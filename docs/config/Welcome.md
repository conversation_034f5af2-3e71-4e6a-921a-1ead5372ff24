# Welcome to the envShell Configuration Documentation

Welcome to the official documentation for configuring **envShell**! This guide will help you understand and customize your `envShell` experience by modifying the configuration files.

## Overview

envShell uses TOML configuration files to manage its settings. These files allow you to tweak various aspects of the environment, such as display settings, dock behavior, panel widgets, and more.

## Getting Started

To get started with configuring envShell:

1. Start envShell, to automatically create the default configuration file.
2. Locate your configuration files, typically found in `~/.config/envshell/`.
3. Open the desired configuration file in your favorite text editor.
4. Modify the settings as needed. Refer to the examples and comments for guidance.
5. Save your changes and restart envShell to apply the new configuration.

## Configuration Sections

Here are the main sections you can configure:

- **General**: Global settings, such as including additional configuration files.
- **Display**: Resolution and display-related options.
- **Window**: Window management settings, including autohide and title translation.
- **Dock**: Customize the dock's appearance, behavior, and pinned applications.
- **Panel**: Configure the top panel, widgets, and date format.
- **ScreenFilter**: Enable and customize screen filters like Nightshift and Redshift.
- **EnvLight**: Manage extensions and search behavior.
- **Workspace**: Workspace-specific settings.
- **System Tray**: Customize the system tray.
- **Shell**: Configure shell settings/apps.
- **Notch**: Customize the notch.
- **Notifications**: Notification settings.
- **Cache**: Cache settings for various features.
- **Bluetooth**: Bluetooth settings.
- **Wifi**: WiFi settings.
- **Misc**: Fun and experimental features like "Activate Linux."
- **MusicPlayer**: Music player settings.
- **Panel Mods**: Mods for the top panel.

## Next Steps?

Now that you have a basic understanding of how the configuration file looks like, you can continue in the next sections to learn more about each section and its options.

- [General Configuration](./General.md)
- [Display Configuration](./Display.md)
- [Window Configuration](./Window.md)
- [Dock Configuration](./Dock.md)
- [Panel Configuration](./Panel.md)
- [ScreenFilter Configuration](./ScreenFilter.md)
- [EnvLight Configuration](./EnvLight.md)
- [Workspace Configuration](./Workspace.md)
- [System Tray Configuration](./Systray.md)
- [Shell Configuration](./Shell.md)
- [Notch Configuration](./Notch.md)
- [Notifications Configuration](./Notifications.md)
- [Cache Configuration](./Cache.md)
- [Bluetooth Configuration](./Bluetooth.md)
- [Wifi Configuration](./Wifi.md)
- [Misc Configuration](./Misc.md)
- [MusicPlayer Configuration](./MusicPlayer.md)
- [Panel Mods Configuration](./Mods.md)

## Need Help?

If you encounter any issues or need further assistance, check out the [envShell GitHub Repository](https://github.com/E3nviction/envshell) for more resources and support.

Happy configuring! 🎉  
