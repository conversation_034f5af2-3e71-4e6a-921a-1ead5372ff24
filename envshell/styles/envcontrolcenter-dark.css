#control-center-menu {
	background-color: transparent;
	border-radius: 15px;
	box-shadow: none;
	margin: 0;
}

.control-center-widgets {
	background-color: alpha(#000, 0.3);
	box-shadow: inset 0 0 0 1px alpha(#aaa, 0.4);
	border: 1px solid alpha(#111, 0.4);
	border-radius: 1.25rem;
	padding: .5rem;
	margin: 5px;
}

#control-center-widgets {
	background-color: alpha(#000, 0.3);
	box-shadow: inset 0 0 0 1px alpha(#aaa, 0.4);
	border: 1px solid alpha(#111, 0.4);
	border-radius: 1.25rem;
	padding: .5rem;
}

/* Widgets */
#wifi-widget,
#bluetooth-widget {
	padding: 10px;
}

.icon {
	background-color: #2369ff;
	font-size: 15px;
	padding: 20px 20px;
}

#bluetooth-widget-label,
#wifi-widget-label {
	font-size: 10px;
	margin-left: 5px;
	font-weight: 400;
	color: #aaa;
}

#bluetooth-widget-title {
	font-size: 12px;
	font-weight: 500;
	color: #fff;
}

#bluetooth-widget-top {
	padding-bottom: 10px;
	border-bottom: 1px solid alpha(#aaa, 0.3);
}

#device-icon {
	border-radius: 50%;
	padding: 5px;
	margin-bottom: 2.5px;
	margin-top: 2.5px;
	margin-right: 10px;
	background-color: #555;
}

#devices-title {
	color: alpha(#fff, 0.6);
}

#device-icon.paired {
	background-color: #888;
}

#device-icon.connected {
	background-color: #2369ff;
}

.menu {
	margin: 5px;
	background-color: alpha(#000, 0.3);
	border: .5px solid alpha(#888, 0.4);
	box-shadow: inset 0 0 200px 0 alpha(#111, 0.3);
	border-radius: 12px;
	padding: 1rem;
}

.title {
	padding: 0;
	font-weight: 500;
	font-size: 12px;
}

.ct {
	margin-left: 5px;
}

#volume-widget-icon {
	margin-top: -24px;
	margin-right: -30px;
	color: alpha(#111, 0.6);
}

#brightness-widget-icon {
	margin-top: -24px;
	margin-right: -25px;
	color: alpha(#111, 0.6);
}

#control-center-menu slider {
	background-image: none;
	background-color: transparent;
	padding: 2px;
	border-radius: 20px;
}

#control-center-menu .small slider {
	background-image: none;
	background-color: transparent;
	padding: 0px;
}

#control-center-menu scale {
	background-color: transparent;
	margin-top: 10px;
	border-radius: 20px;
}

#control-center-menu trough {
    min-width: 25px;
    border-radius: 99px;
	background-color: alpha(#666, 0.5);
	border: 1px solid alpha(#444, 0.3);
}

#control-center-menu highlight {
    background: alpha(#fff, 0.8);
    border-radius: 99px;
}

#control-center-menu mark indicator {
	background: none;
	background-image: none;
	color: alpha(#fff, 0.2);
}

#control-center-menu mark label {
	background: none;
	background-image: none;
	color: alpha(#fff, 0.2);
}