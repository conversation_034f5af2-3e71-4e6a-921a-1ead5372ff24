#!/usr/bin/env python
import argparse
import os
import subprocess
import sys
from utils.socket import create_socket_signal, get_socket_signal


def update_toml_value(file_path, toml_path, value):
	try:
		with open(file_path, "r") as f:
			data = toml.load(f)
	except FileNotFoundError:
		print(f"Error: File '{file_path}' not found.")
		return
	except toml.TomlDecodeError:
		print(f"Error: Invalid TOML syntax in '{file_path}'.")
		return

	keys = toml_path.split(".")
	temp = data
	for key in keys[:-1]:
		temp = temp.setdefault(key, {})

	temp[keys[-1]] = value

	with open(file_path, "w") as f:
		toml.dump(data, f)

	print(f"Successfully updated '{toml_path}' to {value} in '{file_path}'")

def restart_module(module):
	modules = {
		"Dock": "Dock",
		"Panel": "Panel",
		"Notifications": "Notifications",
		"Shell": "envShell",
		"ScreenFilter": "ScreenFilter",
		"all": ["ScreenFilter", "Dock", "Panel", "Notifications"],
	}
	if module not in modules:
		print(f"Error: Invalid module '{module}'.")
		return
	module = modules[module]
	if isinstance(module, list):
		for m in module:
			subprocess.run(["pkill", "-x", m])
	else:
		subprocess.run(["pkill", "-x", module])

def main():
	parser = argparse.ArgumentParser(description="Manage envShell.")
	parser.add_argument("-NC", "--no-config", action="store_true", help="Disable accessing config")
	subparsers = parser.add_subparsers(dest="command", required=True)

	keyword_parser = subparsers.add_parser("keyword", help="Update configs of envShell.")
	send_parser = subparsers.add_parser("send", help="Send a command to envShell.")
	reset_parser = subparsers.add_parser("reset", help="Delete the temporary configuration file.")
	restart_parser = subparsers.add_parser("restart", help="Restart a Shell Module process.")

	keyword_subparsers = keyword_parser.add_subparsers(dest="subcommand", required=True)

	update_parser = keyword_subparsers.add_parser("set", help="Update a setting with a value.")
	get_parser = keyword_subparsers.add_parser("get", help="Get a settings value.")

	update_parser.add_argument("settings_path", help="Path to the settings (e.g., example.path.test)")
	update_parser.add_argument("value", help="New value for the setting")
	get_parser.add_argument("settings_path", help="Path to the setting (e.g., example.path.test)")

	send_parser.add_argument("sendcommand", help="Command to send to envShell.")

	restart_parser.add_argument("module", choices=["Dock", "Panel", "Notifications", "Shell", "ScreenFilter", "all"], help="Module to restart.")

	args = parser.parse_args()
	if not args.no_config:
		global c
		global toml
		import toml
		from config.c import c

	file_path = os.path.join(os.path.expanduser("~"), ".config", "envshell", "envctl.toml")

	if args.command == "keyword":
		if args.subcommand == "set":
			value = args.value

			if value.lower() in ["true", "false"]: value = value.lower() == "true"
			elif value.isdigit(): value = int(value)
			else:
				try: value = float(value)
				except ValueError: pass  # Keep as string

			update_toml_value(file_path, args.settings_path, value)
		elif args.subcommand == "get":
			value = c.get_rule(args.settings_path)
			print(f"Value of '{args.settings_path}': {value}")
		else: parser.print_help()
	elif args.command == "reset":
		if os.path.exists(file_path):
			os.remove(file_path)
			print("Configuration file deleted.")
		else: print("No configuration file to delete.")
	elif args.command == "restart": restart_module(args.module)
	elif args.command == "send":
		create_socket_signal("envctl.socket", "command", {"value": args.sendcommand})
	else: parser.print_help()

if __name__ == "__main__":
	main()
